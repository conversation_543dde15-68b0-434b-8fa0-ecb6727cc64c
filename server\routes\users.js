const express = require('express');
const User = require('../models/User');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// @desc    Get all users (Admin only)
// @route   GET /api/users
// @access  Private/Admin
router.get('/', protect, authorize('admin'), async (req, res, next) => {
  try {
    const users = await User.find().select('-password');
    res.json({
      success: true,
      data: users,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get user by blog domain
// @route   GET /api/users/blog/:domain
// @access  Public
router.get('/blog/:domain', async (req, res, next) => {
  try {
    const user = await User.findOne({ blogDomain: req.params.domain });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Blog not found',
      });
    }

    res.json({
      success: true,
      data: user.getPublicProfile(),
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
