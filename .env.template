# =============================================================================
# RIVSY BLOG PLATFORM - ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env files to version control

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URI=mongodb://localhost:27017/rivsy-blog
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/rivsy-blog

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=5000
CLIENT_URL=http://localhost:3000
SERVER_URL=http://localhost:5000

# =============================================================================
# JWT & AUTHENTICATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret-change-this
REFRESH_TOKEN_EXPIRE=30d

# =============================================================================
# AI INTEGRATION - LITELLM CONFIGURATION
# =============================================================================
# LiteLLM supports multiple AI providers. Configure one or more:

# Azure OpenAI
AZURE_API_KEY=your-azure-openai-api-key
AZURE_API_BASE=https://your-resource.openai.azure.com/
AZURE_API_VERSION=2023-12-01-preview
AZURE_DEPLOYMENT_NAME=gpt-4

# AWS Bedrock
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# OpenAI (Alternative)
OPENAI_API_KEY=your-openai-api-key

# Anthropic Claude (Alternative)
ANTHROPIC_API_KEY=your-anthropic-api-key

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Rivsy Blog Platform

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# SendGrid (Alternative)
SENDGRID_API_KEY=your-sendgrid-api-key

# =============================================================================
# FILE STORAGE & CDN
# =============================================================================
# AWS S3 Configuration
AWS_S3_BUCKET=rivsy-blog-assets
AWS_S3_REGION=us-east-1
AWS_S3_ACCESS_KEY=your-s3-access-key
AWS_S3_SECRET_KEY=your-s3-secret-key

# Cloudinary (Alternative)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# =============================================================================
# SEO & ANALYTICS
# =============================================================================
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
GOOGLE_SEARCH_CONSOLE_VERIFICATION=your-verification-code
BING_WEBMASTER_VERIFICATION=your-bing-verification

# IndexNow API Key (for instant search engine indexing)
INDEXNOW_API_KEY=your-indexnow-api-key

# =============================================================================
# SOCIAL MEDIA INTEGRATION
# =============================================================================
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
FACEBOOK_APP_ID=your-facebook-app-id
LINKEDIN_CLIENT_ID=your-linkedin-client-id

# =============================================================================
# NEWSLETTER & MARKETING
# =============================================================================
MAILCHIMP_API_KEY=your-mailchimp-api-key
MAILCHIMP_LIST_ID=your-list-id

# ConvertKit (Alternative)
CONVERTKIT_API_KEY=your-convertkit-api-key
CONVERTKIT_FORM_ID=your-form-id

# =============================================================================
# SECURITY & RATE LIMITING
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_SALT_ROUNDS=12

# =============================================================================
# DEVELOPMENT & DEBUGGING
# =============================================================================
DEBUG=rivsy:*
LOG_LEVEL=info

# =============================================================================
# PRODUCTION DEPLOYMENT
# =============================================================================
# SSL Certificate paths (for production)
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/private-key.pem

# Domain configuration
DOMAIN=yourdomain.com
SUBDOMAIN_SUPPORT=true

# CDN Configuration
CDN_URL=https://cdn.yourdomain.com
STATIC_FILES_URL=https://static.yourdomain.com
