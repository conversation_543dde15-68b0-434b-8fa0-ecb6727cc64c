const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Post = require('../models/Post');
const User = require('../models/User');
const { protect, authorize, optionalAuth, checkOwnership } = require('../middleware/auth');

const router = express.Router();

// @desc    Get all published posts
// @route   GET /api/posts
// @access  Public
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('category').optional().trim(),
  query('tag').optional().trim(),
  query('search').optional().trim(),
  query('author').optional().isMongoId().withMessage('Author must be a valid ID'),
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const {
      page = 1,
      limit = 10,
      category,
      tag,
      search,
      author,
      sortBy = 'publishedAt',
      sortOrder = 'desc',
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      category,
      tag,
      search,
      author,
    };

    const posts = await Post.getPublished(options);
    
    // Get total count for pagination
    const query = { status: 'published' };
    if (category) query.categories = category;
    if (tag) query.tags = tag;
    if (author) query.author = author;
    if (search) query.$text = { $search: search };
    
    const total = await Post.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: posts,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalPosts: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get single post by slug
// @route   GET /api/posts/:slug
// @access  Public
router.get('/:slug', optionalAuth, async (req, res, next) => {
  try {
    const post = await Post.findOne({ 
      slug: req.params.slug,
      status: 'published'
    }).populate('author', 'name avatar bio blogDomain socialLinks');

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found',
      });
    }

    // Increment views (don't await to avoid slowing response)
    post.incrementViews().catch(err => console.error('Error incrementing views:', err));

    res.json({
      success: true,
      data: post,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Create new post
// @route   POST /api/posts
// @access  Private
router.post('/', protect, [
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title is required and must be less than 200 characters'),
  body('content')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Content is required'),
  body('excerpt')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Excerpt must be less than 500 characters'),
  body('categories')
    .optional()
    .isArray()
    .withMessage('Categories must be an array'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('status')
    .optional()
    .isIn(['draft', 'published'])
    .withMessage('Status must be either draft or published'),
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const postData = {
      ...req.body,
      author: req.user._id,
    };

    const post = await Post.create(postData);
    
    // Update user's total posts count
    await User.findByIdAndUpdate(req.user._id, {
      $inc: { totalPosts: 1 }
    });

    // Populate author data
    await post.populate('author', 'name avatar bio blogDomain');

    res.status(201).json({
      success: true,
      message: 'Post created successfully',
      data: post,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update post
// @route   PUT /api/posts/:id
// @access  Private (Author or Admin)
router.put('/:id', protect, checkOwnership(Post), [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be less than 200 characters'),
  body('content')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Content cannot be empty'),
  body('excerpt')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Excerpt must be less than 500 characters'),
  body('categories')
    .optional()
    .isArray()
    .withMessage('Categories must be an array'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('status')
    .optional()
    .isIn(['draft', 'published', 'archived'])
    .withMessage('Status must be draft, published, or archived'),
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const allowedFields = [
      'title', 'content', 'excerpt', 'categories', 'tags', 'status',
      'featuredImage', 'metaTitle', 'metaDescription', 'keywords',
      'scheduledFor', 'socialSharing'
    ];

    const updateData = {};
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key)) {
        updateData[key] = req.body[key];
      }
    });

    const post = await Post.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('author', 'name avatar bio blogDomain');

    res.json({
      success: true,
      message: 'Post updated successfully',
      data: post,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Delete post
// @route   DELETE /api/posts/:id
// @access  Private (Author or Admin)
router.delete('/:id', protect, checkOwnership(Post), async (req, res, next) => {
  try {
    await Post.findByIdAndDelete(req.params.id);
    
    // Update user's total posts count
    await User.findByIdAndUpdate(req.user._id, {
      $inc: { totalPosts: -1 }
    });

    res.json({
      success: true,
      message: 'Post deleted successfully',
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get user's posts (drafts and published)
// @route   GET /api/posts/my/posts
// @access  Private
router.get('/my/posts', protect, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('status').optional().isIn(['draft', 'published', 'archived']).withMessage('Invalid status'),
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const {
      page = 1,
      limit = 10,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = req.query;

    const query = { author: req.user._id };
    if (status) query.status = status;

    const posts = await Post.find(query)
      .sort({ [sortBy]: sortOrder === 'desc' ? -1 : 1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('author', 'name avatar');

    const total = await Post.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: posts,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalPosts: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get post analytics
// @route   GET /api/posts/:id/analytics
// @access  Private (Author or Admin)
router.get('/:id/analytics', protect, checkOwnership(Post), async (req, res, next) => {
  try {
    const post = req.resource;

    const analytics = {
      views: post.views,
      likes: post.likes,
      shares: post.shares,
      comments: post.comments.length,
      approvedComments: post.comments.filter(c => c.approved).length,
      readingTime: post.readingTime,
      seoScore: post.seoScore,
      publishedAt: post.publishedAt,
      lastUpdated: post.updatedAt,
    };

    res.json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
