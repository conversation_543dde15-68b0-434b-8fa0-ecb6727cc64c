{"name": "rivsy-client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "rehype-raw": "^7.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react-hot-toast": "^2.4.1", "react-helmet-async": "^2.0.4", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "react-intersection-observer": "^9.5.3", "react-dropzone": "^14.2.3", "react-quill": "^2.0.0", "quill": "^1.3.7", "prismjs": "^1.29.0", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "vitest": "^1.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5"}}