{"name": "rivsy-server", "version": "1.0.0", "description": "Backend API server for Rivsy blog platform", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "seed": "node scripts/seedDatabase.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "slugify": "^1.6.6", "marked": "^11.1.1", "dompurify": "^3.0.7", "jsdom": "^23.0.1", "sitemap": "^7.1.1", "rss": "^1.2.2", "node-cron": "^3.0.3", "axios": "^1.6.2", "litellm": "^1.0.0", "nodemailer": "^6.9.7", "compression": "^1.7.4", "express-mongo-sanitize": "^2.2.0", "xss": "^1.0.14"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}