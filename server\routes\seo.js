const express = require('express');
const { protect } = require('../middleware/auth');
const seoService = require('../services/seoService');
const Post = require('../models/Post');

const router = express.Router();

// @desc    Generate and serve sitemap
// @route   GET /api/seo/sitemap
// @access  Public
router.get('/sitemap', async (req, res, next) => {
  try {
    const sitemap = await seoService.generateSitemap();

    res.set({
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
    });

    res.send(sitemap);
  } catch (error) {
    next(error);
  }
});

// @desc    Generate and serve RSS feed
// @route   GET /api/seo/rss
// @access  Public
router.get('/rss', async (req, res, next) => {
  try {
    const rss = await seoService.generateRSSFeed();

    res.set({
      'Content-Type': 'application/rss+xml',
      'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
    });

    res.send(rss);
  } catch (error) {
    next(error);
  }
});

// @desc    Generate robots.txt
// @route   GET /api/seo/robots
// @access  Public
router.get('/robots', async (req, res, next) => {
  try {
    const robotsTxt = seoService.generateRobotsTxt();

    res.set({
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400' // Cache for 24 hours
    });

    res.send(robotsTxt);
  } catch (error) {
    next(error);
  }
});

// @desc    Generate LLMs.txt for AI crawlers
// @route   GET /api/seo/llms
// @access  Public
router.get('/llms', async (req, res, next) => {
  try {
    const llmsTxt = seoService.generateLLMsTxt();

    res.set({
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400' // Cache for 24 hours
    });

    res.send(llmsTxt);
  } catch (error) {
    next(error);
  }
});

// @desc    Get SEO analysis for a post
// @route   GET /api/seo/analyze/:id
// @access  Private
router.get('/analyze/:id', protect, async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    // Check if user owns the post or is admin
    if (post.author.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const analysis = seoService.analyzeSEOScore(post);

    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get meta tags for a post
// @route   GET /api/seo/meta/:slug
// @access  Public
router.get('/meta/:slug', async (req, res, next) => {
  try {
    const post = await Post.findOne({
      slug: req.params.slug,
      status: 'published'
    }).populate('author', 'name socialLinks blogDomain');

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    const metaTags = seoService.generateMetaTags(post, post.author);
    const schema = seoService.generatePostSchema(post, post.author);

    res.json({
      success: true,
      data: {
        metaTags,
        schema
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get structured data schemas
// @route   GET /api/seo/schema/:type
// @access  Public
router.get('/schema/:type', async (req, res, next) => {
  try {
    const { type } = req.params;
    let schema;

    switch (type) {
      case 'organization':
        schema = seoService.generateOrganizationSchema();
        break;
      case 'website':
        schema = seoService.generateWebsiteSchema();
        break;
      case 'breadcrumb':
        // Example breadcrumb - in real app, this would come from request
        const breadcrumbs = [
          { name: 'Home', url: '/' },
          { name: 'Blog', url: '/blog' },
          { name: 'Post Title', url: '/blog/post-slug' }
        ];
        schema = seoService.generateBreadcrumbSchema(breadcrumbs);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid schema type'
        });
    }

    res.json({
      success: true,
      data: schema
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Ping search engines about new content
// @route   POST /api/seo/ping
// @access  Private
router.post('/ping', protect, async (req, res, next) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL is required'
      });
    }

    // In a real implementation, you would ping search engines here
    // For now, we'll just return success

    res.json({
      success: true,
      message: 'Search engines notified successfully',
      data: {
        url,
        pingedAt: new Date().toISOString(),
        services: ['Google', 'Bing', 'IndexNow']
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
