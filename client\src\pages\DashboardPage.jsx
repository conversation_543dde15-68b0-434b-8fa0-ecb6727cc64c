import { Helmet } from 'react-helmet-async';
import { useAuth } from '../contexts/AuthContext';

const DashboardPage = () => {
  const { user } = useAuth();

  return (
    <>
      <Helmet>
        <title>Dashboard - Rivsy</title>
        <meta name="description" content="Manage your blog and view analytics." />
      </Helmet>
      
      <div className="min-h-screen bg-white dark:bg-gray-900 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8">
            Welcome back, {user?.name}!
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Dashboard page - to be implemented with analytics, post management, etc.
          </p>
        </div>
      </div>
    </>
  );
};

export default DashboardPage;
