{"name": "rivsy-blog-platform", "version": "1.0.0", "description": "A complete MERN stack blogging platform that competes with Superblog.ai", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install-all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test && cd ../client && npm test"}, "keywords": ["blog", "mern", "react", "nodejs", "mongodb", "express", "ai", "seo", "cms"], "author": "Rivsy Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}