<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rivsy - Modern Blogging Platform</title>
    <meta name="description" content="Create beautiful, SEO-optimized blogs with AI-powered content assistance. The modern alternative to traditional blogging platforms." />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://rivsy.com/" />
    <meta property="og:title" content="Rivsy - Modern Blogging Platform" />
    <meta property="og:description" content="Create beautiful, SEO-optimized blogs with AI-powered content assistance." />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://rivsy.com/" />
    <meta property="twitter:title" content="Rivsy - Modern Blogging Platform" />
    <meta property="twitter:description" content="Create beautiful, SEO-optimized blogs with AI-powered content assistance." />
    <meta property="twitter:image" content="/twitter-image.jpg" />
    
    <!-- Theme color -->
    <meta name="theme-color" content="#6366f1" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
